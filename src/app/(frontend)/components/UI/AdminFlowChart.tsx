'use client';

import {
  Background,
  Controls,
  Edge,
  Handle,
  Node,
  Position,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useEffect, useState } from 'react';
import CircularLoader from './CircularLoader';

// Modern node component matching the admin editor design
const CustomNode = ({ data }: { data: any }) => {
  return (
    <div className="group relative p-5 border border-gray-200 rounded-xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 max-w-md">
      {/* Gradient border effect on hover */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        style={{
          background: 'linear-gradient(to right, #6366f1, #8b5cf6, #d946ef)',
          padding: '1px',
          margin: '-1px',
          zIndex: -1,
        }}
      />

      {/* Node content with improved typography */}
      <div className="text-lg font-semibold text-gray-800 mb-1 text-center">{data.label}</div>

      {/* Handles with improved styling - matching admin editor */}
      <Handle
        type="source"
        position={Position.Top}
        id="top"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ top: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Top}
        id="top-target"
        className="w-2 h-2 bg-emerald-500 border-2 border-white"
        style={{ top: -6, left: '55%', borderRadius: '50%' }}
      />

      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ bottom: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Bottom}
        id="bottom-target"
        className="w-2 h-2 bg-emerald-500 border-2 border-white"
        style={{ bottom: -6, left: '55%', borderRadius: '50%' }}
      />

      <Handle
        type="source"
        position={Position.Left}
        id="left"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ left: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Left}
        id="left-target"
        className="w-2 h-2 bg-emerald-500 border-2 border-white"
        style={{ left: -6, top: '55%', borderRadius: '50%' }}
      />

      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className="w-3 h-3 bg-indigo-500 border-2 border-white"
        style={{ right: -8, borderRadius: '50%' }}
      />
      <Handle
        type="target"
        position={Position.Right}
        id="right-target"
        className="w-2 h-2 bg-emerald-500 border-2 border-white"
        style={{ right: -6, top: '55%', borderRadius: '50%' }}
      />
    </div>
  );
};

const nodeTypes = {
  editable: CustomNode,
};

interface AdminFlowChartProps {
  flowchartId?: string;
  className?: string;
}

const AdminFlowChart = ({ flowchartId, className = '' }: AdminFlowChartProps) => {
  const [flowchartData, setFlowchartData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flowNodes, setFlowNodes] = useNodesState<any>([]);
  const [flowEdges, setFlowEdges] = useEdgesState<any>([]);

  useEffect(() => {
    const fetchFlowchartData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch flowchart demo data
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/flowchart-demo${flowchartId ? `/${flowchartId}` : ''}`,
          { cache: 'no-store' },
        );

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Fetched flowchart data:', data);

        // If fetching a specific flowchart, use that data
        // If fetching all, use the first one for demo
        const flowchart = flowchartId ? data : data.docs?.[0];

        if (flowchart?.flowchartConfig) {
          setFlowchartData(flowchart.flowchartConfig);
        } else {
          setError('No flowchart configuration found');
        }
      } catch (err) {
        console.error('Error fetching flowchart data:', err);
        setError('Failed to load flowchart data');
      } finally {
        setLoading(false);
      }
    };

    fetchFlowchartData();
  }, [flowchartId]);

  useEffect(() => {
    if (!flowchartData?.nodes || !flowchartData?.edges) return;

    console.log('Processing flowchart data:', flowchartData);

    // Convert admin flowchart data to ReactFlow format
    const nodes: Node[] = flowchartData.nodes.map((node: any) => ({
      id: node.id,
      type: 'editable',
      data: { label: node.label },
      position: node.position || { x: 0, y: 0 },
    }));

    const edges: Edge[] = flowchartData.edges.map((edge: any) => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle,
      label: edge.label,
      animated: true,
      style: {
        stroke: edge.isInnovationClass ? '#a855f7' : '#6366f1',
        strokeWidth: edge.isInnovationClass ? 3 : 2,
        strokeDasharray: edge.isInnovationClass ? '8,4' : undefined,
      },
      labelStyle: {
        fontSize: 12,
        fontWeight: edge.isInnovationClass ? 600 : 500,
        color: edge.isInnovationClass ? '#7c3aed' : '#374151',
        background: edge.isInnovationClass
          ? 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)'
          : 'white',
        border: edge.isInnovationClass ? '2px solid #a855f7' : '1px solid #e5e7eb',
        borderRadius: '8px',
        padding: '6px 12px',
      },
    }));

    // Use admin-configured positions directly
    setFlowNodes(nodes);
    setFlowEdges(edges);
  }, [flowchartData, setFlowNodes, setFlowEdges]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <CircularLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="text-red-500 text-lg font-semibold mb-2">Error Loading Flowchart</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!flowchartData || !flowNodes.length) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="text-gray-500 text-lg font-semibold mb-2">No Flowchart Data</div>
          <div className="text-gray-400">Create a flowchart in the admin panel to see it here</div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`w-full h-[600px] border border-gray-200 rounded-xl shadow-lg bg-white ${className}`}
    >
      <ReactFlow
        nodes={flowNodes}
        edges={flowEdges}
        nodeTypes={nodeTypes}
        fitView={true}
        fitViewOptions={{
          padding: 0.2,
          minZoom: 0.5,
          maxZoom: 1.5,
        }}
        proOptions={{ hideAttribution: true }}
        className="bg-white"
      >
        <Background
          gap={20}
          size={1}
          color="#e5e7eb"
          lineWidth={2}
          style={{ backgroundColor: '#ffffff' }}
        />
        <Controls className="bg-white shadow-md rounded-md border border-gray-200" />
      </ReactFlow>
    </div>
  );
};

export default AdminFlowChart;
