'use client';

import { useEffect, useState } from 'react';
import AdminFlow<PERSON>hart from '../components/UI/AdminFlowChart';

interface FlowchartDemo {
  id: string;
  name: string;
  description?: string;
  flowchartConfig?: any;
  createdAt: string;
  updatedAt: string;
}

const FlowchartDemoPage = () => {
  const [flowchartDemos, setFlowchartDemos] = useState<FlowchartDemo[]>([]);
  const [selectedDemo, setSelectedDemo] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [useAutoLayout, setUseAutoLayout] = useState(false);

  useEffect(() => {
    const fetchFlowchartDemos = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/flowchart-demo`, {
          cache: 'no-store',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Fetched flowchart demos:', data);

        if (data.docs && data.docs.length > 0) {
          setFlowchartDemos(data.docs);
          setSelectedDemo(data.docs[0].id); // Select first demo by default
        } else {
          setError('No flowchart demos found');
        }
      } catch (err) {
        console.error('Error fetching flowchart demos:', err);
        setError('Failed to load flowchart demos');
      } finally {
        setLoading(false);
      }
    };

    fetchFlowchartDemos();
  }, []);

  const selectedDemoData = flowchartDemos.find((demo) => demo.id === selectedDemo);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">🎨 Flowchart Demo Showcase</h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Experience the power of our admin-configured flowcharts! Create beautiful, interactive
              flowcharts in the admin panel and see them come to life here.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <div className="text-gray-600">Loading flowchart demos...</div>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
              <div className="text-red-600 text-lg font-semibold mb-2">⚠️ Error</div>
              <div className="text-red-700">{error}</div>
              <div className="mt-4 text-sm text-red-600">
                Make sure to create some flowchart demos in the admin panel first!
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Demo Selector */}
            {flowchartDemos.length > 1 && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  📋 Select a Flowchart Demo
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {flowchartDemos.map((demo) => (
                    <button
                      key={demo.id}
                      onClick={() => setSelectedDemo(demo.id)}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                        selectedDemo === demo.id
                          ? 'border-blue-500 bg-blue-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                      }`}
                    >
                      <div className="font-semibold text-gray-900 mb-1">{demo.name}</div>
                      {demo.description && (
                        <div className="text-sm text-gray-600 line-clamp-2">{demo.description}</div>
                      )}
                      <div className="text-xs text-gray-400 mt-2">
                        Updated: {new Date(demo.updatedAt).toLocaleDateString()}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Selected Demo Info */}
            {selectedDemoData && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      🎯 {selectedDemoData.name}
                    </h2>
                    {selectedDemoData.description && (
                      <p className="text-gray-600">{selectedDemoData.description}</p>
                    )}
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    <div>Created: {new Date(selectedDemoData.createdAt).toLocaleDateString()}</div>
                    <div>Updated: {new Date(selectedDemoData.updatedAt).toLocaleDateString()}</div>
                  </div>
                </div>

                {/* Flowchart Stats */}
                {selectedDemoData.flowchartConfig && (
                  <div className="flex items-center space-x-6 text-sm text-gray-600 mb-6">
                    <div className="flex items-center space-x-2">
                      <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                      <span>{selectedDemoData.flowchartConfig.nodes?.length || 0} Nodes</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="w-3 h-3 bg-purple-500 rounded-full"></span>
                      <span>{selectedDemoData.flowchartConfig.edges?.length || 0} Connections</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="w-3 h-3 bg-emerald-500 rounded-full"></span>
                      <span>
                        {selectedDemoData.flowchartConfig.edges?.filter(
                          (edge: any) => edge.isInnovationClass,
                        )?.length || 0}{' '}
                        Innovation Classes
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Flowchart Display */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">🚀 Interactive Flowchart</h3>

                {/* Layout Toggle */}
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">Layout:</span>
                  <button
                    onClick={() => setUseAutoLayout(!useAutoLayout)}
                    className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                      useAutoLayout
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    {useAutoLayout ? '🤖 Auto-Layout' : '🎨 Admin Layout'}
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="text-sm text-gray-600">
                  <strong>Current Mode:</strong>{' '}
                  {useAutoLayout
                    ? 'Auto-Layout (Dagre algorithm arranges nodes automatically)'
                    : 'Admin Layout (Uses exact positions from admin editor)'}
                  <br />
                  <strong>Features:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>🎨 Modern design matching the admin editor</li>
                    <li>🔗 Preserved handle connections (exact same positions)</li>
                    <li>💜 Innovation Class edges with special styling</li>
                    <li>📍 Custom edge label positioning</li>
                    <li>🎯 Interactive zoom and pan controls</li>
                    <li>✨ Smooth animations and hover effects</li>
                  </ul>
                </div>
              </div>

              <AdminFlowChart
                flowchartId={selectedDemo || undefined}
                className="border-2 border-dashed border-gray-300"
                useAutoLayout={useAutoLayout}
              />
            </div>

            {/* Instructions */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                🛠️ How to Create Your Own Flowchart
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Admin Panel Steps:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                    <li>Go to Admin Panel → Demo → Flowchart Demos</li>
                    <li>Click "Create New" to add a new flowchart</li>
                    <li>Use the modern flowchart editor to design your flow</li>
                    <li>Add nodes, connect them with handles</li>
                    <li>Name your connections and set Innovation Classes</li>
                    <li>Save and see it appear here instantly!</li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-2">Editor Features:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                    <li>✏️ Click nodes to edit labels</li>
                    <li>📍 Drag edge labels to reposition</li>
                    <li>🔗 8 handles per node for flexible connections</li>
                    <li>💜 Innovation Class edges with special styling</li>
                    <li>💾 Handle positions automatically saved</li>
                    <li>🎨 Modern design with smooth animations</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FlowchartDemoPage;
