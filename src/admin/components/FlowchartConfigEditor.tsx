'use client';
import { useField } from '@payloadcms/ui';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import React<PERSON>low, {
  addEdge,
  Background,
  Connection,
  Controls,
  Handle,
  Position,
  ReactFlowProvider,
  useEdgesState,
  useNodesState,
} from 'reactflow';
import 'reactflow/dist/style.css';
import styles from './FlowchartConfigEditor.module.css';

// Custom node component with editable labels
const EditableNode = ({ data, id }: { data: any; id: string }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data.label);

  useEffect(() => {
    setEditValue(data.label);
  }, [data.label]);

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    if (editValue.trim()) {
      data.onUpdate(id, { label: editValue.trim() });
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data.label);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  const handleDelete = () => {
    if (confirm('Delete this node?')) {
      data.onDelete(id);
    }
  };

  return (
    <div className={styles.nodeContainer}>
      {/* Gradient border effect on hover */}
      <div className={styles.gradientBorder} />

      <button onClick={handleDelete} className={styles.deleteButton}>
        ×
      </button>

      {isEditing ? (
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={handleSave}
          onKeyDown={handleKeyPress}
          className={styles.nodeInput}
          autoFocus
        />
      ) : (
        <div onClick={handleEdit} className={styles.nodeLabel} title="Click to edit">
          {data.label}
        </div>
      )}

      {/* Four bidirectional handles - one per side */}
      <Handle
        type="source"
        position={Position.Top}
        id="top"
        className={styles.nodeHandle}
        isConnectable={true}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className={styles.nodeHandle}
        isConnectable={true}
      />
      <Handle
        type="source"
        position={Position.Left}
        id="left"
        className={styles.nodeHandle}
        isConnectable={true}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        className={styles.nodeHandle}
        isConnectable={true}
      />
    </div>
  );
};

// Custom edge component with editable labels and draggable positioning
const EditableEdge = ({ id, sourceX, sourceY, targetX, targetY, data, markerEnd }: any) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(data?.label || '');
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [labelOffset, setLabelOffset] = useState({ x: 0, y: 0 });

  // Calculate edge path - use smooth curve for better visual appeal
  const centerX = (sourceX + targetX) / 2;
  const centerY = (sourceY + targetY) / 2;
  const offsetX = (targetY - sourceY) * 0.2; // Perpendicular offset for curve
  const offsetY = (sourceX - targetX) * 0.2;

  const edgePath = `M${sourceX},${sourceY} Q${centerX + offsetX},${centerY + offsetY} ${targetX},${targetY}`;

  // Label position with custom offset
  const labelX = centerX + labelOffset.x;
  const labelY = centerY + labelOffset.y;

  const handleEdit = () => setIsEditing(true);

  const handleSave = () => {
    data.onUpdate(id, { label: editValue.trim() });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(data?.label || '');
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') handleSave();
    if (e.key === 'Escape') handleCancel();
  };

  const handleDelete = () => {
    if (confirm('Delete this connection?')) {
      data.onDelete(id);
    }
  };

  const handleInsertInnovationClass = () => {
    if (data.onInsertInnovationClass) {
      data.onInsertInnovationClass(id);
    }
  };

  // Handle label dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isEditing) return;
    setIsDragging(true);
    const startX = e.clientX;
    const startY = e.clientY;
    const startOffsetX = labelOffset.x;
    const startOffsetY = labelOffset.y;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      setLabelOffset({
        x: startOffsetX + deltaX,
        y: startOffsetY + deltaY,
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const isInnovationClass = data?.isInnovationClass;
  const edgePathClass = isInnovationClass ? styles.innovationEdgePath : styles.edgePath;

  return (
    <g onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <path id={id} d={edgePath} className={edgePathClass} />

      {/* Action buttons - positioned near the middle of the edge */}
      {isHovered && !isEditing && (
        <>
          {/* Insert Innovation Class button */}
          <foreignObject
            x={labelX + 25}
            y={labelY - 30}
            width={20}
            height={20}
            className={styles.edgeInputContainer}
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <div style={{ position: 'relative', pointerEvents: 'auto' }}>
              <button
                onClick={handleInsertInnovationClass}
                className={styles.edgeInsertButton}
                title="Insert Innovation Class"
                style={{ zIndex: 1000 }}
              >
                +
              </button>
            </div>
          </foreignObject>

          {/* Delete button */}
          <foreignObject
            x={labelX + 45}
            y={labelY - 30}
            width={20}
            height={20}
            className={styles.edgeInputContainer}
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <div style={{ position: 'relative', pointerEvents: 'auto' }}>
              <button
                onClick={handleDelete}
                className={styles.edgeDeleteButton}
                title="Delete connection"
                style={{ zIndex: 1000 }}
              >
                ×
              </button>
            </div>
          </foreignObject>
        </>
      )}

      {/* Label/Input area */}
      <foreignObject
        x={labelX - 50}
        y={labelY - 10}
        width={100}
        height={20}
        className={styles.edgeInputContainer}
      >
        {isEditing ? (
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyPress}
            className={styles.edgeInput}
            autoFocus
          />
        ) : (
          <div
            onClick={handleEdit}
            onMouseDown={handleMouseDown}
            className={isInnovationClass ? styles.innovationEdgeLabel : styles.edgeLabel}
            title="Click to edit, drag to reposition"
            style={{
              cursor: isDragging ? 'grabbing' : 'grab',
              userSelect: 'none',
            }}
          >
            {data?.label || 'Click to name'}
          </div>
        )}
      </foreignObject>
    </g>
  );
};

const nodeTypes = {
  editable: EditableNode,
};

const edgeTypes = {
  editable: EditableEdge,
};

const FlowchartConfigEditorComponent: React.FC<{ path: string; name: string }> = () => {
  const { value, setValue } = useField<any>({ path: 'flowchartConfig' });

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const updateFieldValueRef = useRef<((updatedNodes?: any[], updatedEdges?: any[]) => void) | null>(
    null,
  );

  // Update field value with current nodes and edges
  const updateFieldValue = useCallback(
    (updatedNodes?: any[], updatedEdges?: any[]) => {
      const currentNodes = updatedNodes || nodes;
      const currentEdges = updatedEdges || edges;

      const config = {
        nodes: currentNodes.map((node) => ({
          id: node.id,
          label: node.data.label,
          position: node.position,
        })),
        edges: currentEdges.map((edge) => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle || null,
          targetHandle: edge.targetHandle || null,
          label: edge.data?.label || '',
          isInnovationClass: edge.data?.isInnovationClass || false,
        })),
      };

      console.log('Saving flowchart config:', config);
      console.log(
        'Edge details:',
        config.edges.map((edge) => ({
          id: edge.id,
          connection: `${edge.source}(${edge.sourceHandle}) -> ${edge.target}(${edge.targetHandle})`,
          label: edge.label,
        })),
      );
      setValue(config);
    },
    [nodes, edges, setValue],
  );

  updateFieldValueRef.current = updateFieldValue;

  // Handle node position changes (when dragged)
  useEffect(() => {
    if (isInitialized) {
      updateFieldValue();
    }
  }, [nodes, edges, isInitialized, updateFieldValue]);

  // Handle node updates
  const handleNodeUpdate = useCallback(
    (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) => {
        const updatedNodes = nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        );
        // Update field value immediately with the new nodes
        updateFieldValue(updatedNodes, edges);
        return updatedNodes;
      });
    },
    [setNodes, updateFieldValue, edges],
  );

  // Handle node deletion
  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== nodeId);
        setEdges((eds) => {
          const updatedEdges = eds.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId,
          );
          // Update field value immediately with both updated nodes and edges
          updateFieldValue(updatedNodes, updatedEdges);
          return updatedEdges;
        });
        return updatedNodes;
      });
    },
    [setNodes, setEdges, updateFieldValue],
  );

  // Handle edge updates
  const handleEdgeUpdate = useCallback(
    (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) => {
        const updatedEdges = eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        );
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [setEdges, updateFieldValue, nodes],
  );

  // Handle edge deletion
  const handleEdgeDelete = useCallback(
    (edgeId: string) => {
      setEdges((eds) => {
        const updatedEdges = eds.filter((edge) => edge.id !== edgeId);
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [setEdges, updateFieldValue, nodes],
  );

  // Handle setting edge as innovation class
  const handleInsertInnovationClass = useCallback(
    (edgeId: string) => {
      setEdges((eds) => {
        const updatedEdges = eds.map((edge) =>
          edge.id === edgeId
            ? {
                ...edge,
                data: {
                  ...edge.data,
                  label: 'Innovation Class',
                  isInnovationClass: true,
                },
              }
            : edge,
        );
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [setEdges, updateFieldValue, nodes],
  );

  // Initialize from existing data
  useEffect(() => {
    if (isInitialized || !value) return;

    console.log('Initializing flowchart config:', value);

    const stableHandleNodeUpdate = (nodeId: string, updates: { label?: string }) => {
      setNodes((nds) => {
        const updatedNodes = nds.map((node) =>
          node.id === nodeId ? { ...node, data: { ...node.data, ...updates } } : node,
        );
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setEdges((currentEdges) => {
            updateFieldValueRef.current!(updatedNodes, currentEdges);
            return currentEdges;
          });
        }
        return updatedNodes;
      });
    };

    const stableHandleNodeDelete = (nodeId: string) => {
      setNodes((nds) => {
        const updatedNodes = nds.filter((node) => node.id !== nodeId);
        setEdges((eds) => {
          const updatedEdges = eds.filter(
            (edge) => edge.source !== nodeId && edge.target !== nodeId,
          );
          // Update field value immediately
          if (updateFieldValueRef.current) {
            updateFieldValueRef.current!(updatedNodes, updatedEdges);
          }
          return updatedEdges;
        });
        return updatedNodes;
      });
    };

    const stableHandleEdgeUpdate = (edgeId: string, updates: { label?: string }) => {
      setEdges((eds) => {
        const updatedEdges = eds.map((edge) =>
          edge.id === edgeId ? { ...edge, data: { ...edge.data, ...updates } } : edge,
        );
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setNodes((currentNodes) => {
            updateFieldValueRef.current!(currentNodes, updatedEdges);
            return currentNodes;
          });
        }
        return updatedEdges;
      });
    };

    const stableHandleEdgeDelete = (edgeId: string) => {
      setEdges((eds) => {
        const updatedEdges = eds.filter((edge) => edge.id !== edgeId);
        // Update field value immediately
        if (updateFieldValueRef.current) {
          setNodes((currentNodes) => {
            updateFieldValueRef.current!(currentNodes, updatedEdges);
            return currentNodes;
          });
        }
        return updatedEdges;
      });
    };

    const stableHandleInsertInnovationClass = (edgeId: string) => {
      // Use the main handler through ref to avoid stale closures
      if (handleInsertInnovationClass) {
        handleInsertInnovationClass(edgeId);
      }
    };

    if (value?.nodes?.length > 0) {
      const flowNodes = value.nodes.map((node: any) => ({
        id: node.id,
        type: 'editable',
        data: {
          label: node.label,
          onUpdate: stableHandleNodeUpdate,
          onDelete: stableHandleNodeDelete,
        },
        position: node.position || { x: Math.random() * 300, y: Math.random() * 200 },
      }));

      const flowEdges = (value.edges || []).map((edge: any) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        type: 'editable',
        data: {
          label: edge.label,
          isInnovationClass: edge.isInnovationClass || false,
          onUpdate: stableHandleEdgeUpdate,
          onDelete: stableHandleEdgeDelete,
          onInsertInnovationClass: stableHandleInsertInnovationClass,
        },
      }));

      console.log(
        'Loading edges with handles:',
        flowEdges.map((edge: any) => ({
          id: edge.id,
          connection: `${edge.source}(${edge.sourceHandle}) -> ${edge.target}(${edge.targetHandle})`,
          label: edge.data?.label,
        })),
      );

      setNodes(flowNodes);
      setEdges(flowEdges);
    } else {
      // Create default nodes
      const defaultNodes = [
        {
          id: 'node-1',
          type: 'editable',
          data: {
            label: 'Start',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 100, y: 100 },
        },
        {
          id: 'node-2',
          type: 'editable',
          data: {
            label: 'End',
            onUpdate: stableHandleNodeUpdate,
            onDelete: stableHandleNodeDelete,
          },
          position: { x: 300, y: 100 },
        },
      ];
      setNodes(defaultNodes);
    }

    setIsInitialized(true);
  }, [value, isInitialized]);

  // Allow bidirectional connections between any handles
  const isValidConnection = useCallback((connection: Connection) => {
    // Allow connections between different nodes (source-to-source is fine)
    return connection.source !== connection.target;
  }, []);

  // Handle new connections
  const onConnect = useCallback(
    (connection: Connection) => {
      const newEdge = {
        ...connection,
        id: `edge-${Date.now()}`,
        type: 'editable',
        data: {
          label: '',
          onUpdate: handleEdgeUpdate,
          onDelete: handleEdgeDelete,
          onInsertInnovationClass: handleInsertInnovationClass,
        },
      };
      setEdges((eds) => {
        const updatedEdges = addEdge(newEdge, eds);
        // Update field value immediately with the new edges
        updateFieldValue(nodes, updatedEdges);
        return updatedEdges;
      });
    },
    [
      setEdges,
      handleEdgeUpdate,
      handleEdgeDelete,
      handleInsertInnovationClass,
      updateFieldValue,
      nodes,
    ],
  );

  // Add new node
  const addNode = useCallback(() => {
    const newId = `node-${Date.now()}`;
    const newNode = {
      id: newId,
      type: 'editable',
      data: {
        label: 'New Node',
        type: 'Stage',
        onUpdate: handleNodeUpdate,
        onDelete: handleNodeDelete,
      },
      position: { x: Math.random() * 400 + 50, y: Math.random() * 300 + 50 },
    };
    setNodes((nds) => {
      const updatedNodes = [...nds, newNode];
      // Update field value immediately with the new nodes
      updateFieldValue(updatedNodes, edges);
      return updatedNodes;
    });
  }, [setNodes, handleNodeUpdate, handleNodeDelete, updateFieldValue, edges]);

  // Clear all
  const clearAll = useCallback(() => {
    if (confirm('Clear all nodes and edges?')) {
      setNodes([]);
      setEdges([]);
      setValue({ nodes: [], edges: [] });
    }
  }, [setNodes, setEdges, setValue]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3 className={styles.title}>Flowchart Configuration Editor</h3>

        <div className={styles.instructions}>
          <strong className={styles.instructionsTitle}>Modern Flowchart Editor:</strong>
          <ul className={styles.instructionsList}>
            <li>Click on nodes to edit their labels</li>
            <li>Click on edge labels to name connections</li>
            <li>Drag nodes to reposition them</li>
            <li>Drag edge labels to reposition them along the connection</li>
            <li>Connect nodes by dragging from any handle to another node's handle</li>
            <li>Each node has 4 bidirectional handles (top, bottom, left, right)</li>
            <li>🔵 All handles are blue and can both send and receive connections</li>
            <li>Hover over nodes to see delete button with gradient effects</li>
            <li>Hover over edges to see insert (+) and delete (×) buttons</li>
            <li>Click + button on edges to set them as "Innovation Class" connections</li>
            <li>Innovation Class edges appear with purple dashed lines</li>
            <li>Handle positions are automatically saved and restored</li>
            <li>Edges use smooth curves for better visual appeal</li>
          </ul>
        </div>

        <div className={styles.buttonContainer}>
          <button type="button" onClick={addNode} className={styles.addButton}>
            + Add Node
          </button>
          <button type="button" onClick={clearAll} className={styles.clearButton}>
            Clear All
          </button>
        </div>
      </div>

      <div className={styles.flowContainer}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          isValidConnection={isValidConnection}
          connectionMode={'loose' as any}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          fitViewOptions={{ padding: 0.2 }}
          proOptions={{ hideAttribution: true }}
          className="bg-white"
        >
          <Background
            gap={20}
            size={1}
            color="#e5e7eb"
            lineWidth={2}
            style={{ backgroundColor: '#ffffff' }}
          />
          <Controls className="bg-white shadow-md rounded-md border border-gray-200" />
        </ReactFlow>
      </div>

      <div className={styles.stats}>
        <strong>Nodes:</strong> {nodes.length} | <strong>Edges:</strong> {edges.length}
      </div>
    </div>
  );
};

// Export wrapped in provider
const FlowchartConfigEditor = (props: { path: string; name: string }) => {
  return (
    <ReactFlowProvider>
      <FlowchartConfigEditorComponent {...props} />
    </ReactFlowProvider>
  );
};

export default FlowchartConfigEditor;
