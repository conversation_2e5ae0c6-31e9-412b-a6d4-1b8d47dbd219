# 🎨 Flowchart Demo System

A complete flowchart creation and display system with modern admin editor and frontend showcase.

## ✨ Features

### 🛠️ Admin Editor (FlowchartConfigEditor)

- **Modern Design**: Inspired by FlowChart.tsx with gradient effects and smooth animations
- **8-Handle Nodes**: Each node has 4 source (blue) + 4 target (green) handles for maximum flexibility
- **Draggable Edge Labels**: Click and drag edge labels to reposition them anywhere
- **Handle Persistence**: Exact handle connections are saved and restored (e.g., right→left connections stay right→left)
- **Smooth Curves**: Edges use quadratic Bézier curves for natural-looking connections
- **Innovation Class Edges**: Special purple dashed styling for innovation class connections
- **Real-time Updates**: Changes are immediately reflected in the stored configuration

### 🎯 Frontend Display (AdminFlowChart)

- **Consistent Design**: Matches the admin editor's modern aesthetic
- **Preserved Positions**: Uses exact node positions from admin editor (no auto-layout)
- **Handle Restoration**: Displays connections using the exact same handles as created
- **Interactive Controls**: Zoom, pan, and explore the flowchart
- **Responsive Design**: Works on all screen sizes

### 📊 Demo Showcase Page

- **Multi-Demo Support**: Browse and switch between different flowchart configurations
- **Live Statistics**: Shows node count, connection count, and innovation class count
- **Modern UI**: Beautiful gradient backgrounds and card layouts
- **Error Handling**: Graceful fallbacks when no data is available

## 🚀 How to Use

### Creating Flowcharts (Admin Panel)

1. **Access Admin Panel**

   ```
   http://localhost:3001/admin
   ```

2. **Navigate to Flowchart Demos**

   - Go to "Demo" → "Flowchart Demos"
   - Click "Create New"

3. **Design Your Flowchart**

   - **Add Nodes**: Click "Add Node" to create new nodes
   - **Edit Labels**: Click on any node to edit its name
   - **Create Connections**: Drag from any blue handle to any green handle
   - **Name Connections**: Click on edge labels to name them
   - **Reposition Labels**: Drag edge labels to move them around
   - **Innovation Classes**: Click the "+" button on edges to mark them as innovation classes

4. **Save Configuration**
   - The flowchart is automatically saved as you work
   - All handle positions and connections are preserved

### Viewing Flowcharts (Frontend)

1. **Access Demo Page**

   ```
   http://localhost:3001/flowchart-demo
   ```

2. **Browse Demos**

   - Select different flowchart demos from the grid
   - View statistics and information about each demo
   - Interact with the live flowchart display

3. **Navigation**
   - The "Flowchart Demo" link is available in the main navigation menu

## 🎨 Design Features

### Handle System

- **Blue Handles (Source)**: 12px, used for starting connections
- **Green Handles (Target)**: 10px, used for receiving connections
- **8 Handles Per Node**: Top, bottom, left, right (each with source + target)
- **Unique IDs**: Each handle has a unique identifier for precise connections

### Edge Styling

- **Regular Edges**: Blue (#6366f1) with 2px stroke
- **Innovation Class Edges**: Purple (#a855f7) with 3px stroke and dashed pattern
- **Smooth Curves**: Quadratic Bézier curves with calculated control points
- **Draggable Labels**: Custom positioning with visual feedback

### Modern UI Elements

- **Gradient Borders**: Hover effects with rainbow gradients
- **Smooth Animations**: All interactions have 300ms transitions
- **Shadow Effects**: Layered shadows for depth and modern feel
- **Responsive Design**: Works on desktop, tablet, and mobile

## 🔧 Technical Implementation

### Data Structure

```json
{
  "nodes": [
    {
      "id": "node-1",
      "label": "Start Process",
      "position": { "x": 100, "y": 100 }
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "node-1",
      "target": "node-2",
      "sourceHandle": "right",
      "targetHandle": "left-target",
      "label": "Connection Name",
      "isInnovationClass": false
    }
  ]
}
```

### Key Components

- **FlowchartConfigEditor**: Admin editor component
- **AdminFlowChart**: Frontend display component
- **FlowchartDemo**: Payload collection for data storage
- **FlowchartDemoPage**: Demo showcase page

### API Endpoints

- `GET /api/flowchart-demo` - Fetch all flowchart demos
- `GET /api/flowchart-demo/:id` - Fetch specific flowchart demo
- `POST /api/flowchart-demo` - Create new flowchart demo
- `PATCH /api/flowchart-demo/:id` - Update flowchart demo

## 🎯 Use Cases

### Educational Content

- Create process flows for TB treatment procedures
- Design innovation development stages
- Map research methodologies

### Business Processes

- Visualize approval workflows
- Document decision trees
- Create organizational charts

### Interactive Presentations

- Showcase innovation journeys
- Demonstrate system architectures
- Present data flow diagrams

## 🔮 Future Enhancements

- **Export Options**: PDF, PNG, SVG export functionality
- **Templates**: Pre-built flowchart templates
- **Collaboration**: Real-time collaborative editing
- **Version History**: Track changes over time
- **Custom Themes**: Different color schemes and styles
- **Animation Sequences**: Animated flow demonstrations

## 🐛 Troubleshooting

### Common Issues

1. **Connections Not Working**

   - Ensure you're dragging from blue handles to green handles
   - Check that nodes aren't overlapping
   - Verify handle IDs are unique

2. **Labels Not Dragging**

   - Make sure you're not in edit mode
   - Check that the label isn't empty
   - Ensure proper mouse event handling

3. **Data Not Saving**
   - Verify admin permissions
   - Check network connectivity
   - Ensure proper API endpoints

### Debug Mode

Enable console logging to see detailed connection information:

```javascript
console.log(
  'Edge details:',
  config.edges.map((edge) => ({
    id: edge.id,
    connection: `${edge.source}(${edge.sourceHandle}) -> ${edge.target}(${edge.targetHandle})`,
    label: edge.label,
  })),
);
```

## 📝 Contributing

When making changes to the flowchart system:

1. **Maintain Handle Consistency**: Always preserve sourceHandle and targetHandle data
2. **Test Both Sides**: Verify changes work in both admin editor and frontend display
3. **Update Documentation**: Keep this README current with new features
4. **Follow Design System**: Maintain the modern aesthetic and smooth animations

---

**Created with ❤️ for the Atlas of Innovations project**
